#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新编译的RealSense库
"""

import pyrealsense2 as rs
import numpy as np
import time

def test_device_detection():
    """测试设备检测"""
    print("=== 测试RealSense设备检测 ===")
    
    try:
        # 创建context
        ctx = rs.context()
        
        # 查询设备
        devices = ctx.query_devices()
        device_count = len(devices)
        
        print(f"检测到 {device_count} 个RealSense设备")
        
        if device_count == 0:
            print("❌ 没有检测到RealSense设备")
            return False
        
        # 显示设备信息
        for i, device in enumerate(devices):
            print(f"\n设备 {i}:")
            print(f"  名称: {device.get_info(rs.camera_info.name)}")
            print(f"  序列号: {device.get_info(rs.camera_info.serial_number)}")
            print(f"  固件版本: {device.get_info(rs.camera_info.firmware_version)}")
            print(f"  产品线: {device.get_info(rs.camera_info.product_line)}")
            print(f"  产品ID: {device.get_info(rs.camera_info.product_id)}")
            
            # 检查传感器
            sensors = device.query_sensors()
            print(f"  传感器数量: {len(sensors)}")
            
            for j, sensor in enumerate(sensors):
                print(f"    传感器 {j}: {sensor.get_info(rs.camera_info.name)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设备检测失败: {e}")
        return False

def test_data_capture():
    """测试数据捕获"""
    print("\n=== 测试数据捕获 ===")
    
    try:
        # 创建pipeline
        pipeline = rs.pipeline()
        config = rs.config()
        
        # 配置流
        config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
        
        # 启动pipeline
        print("启动数据流...")
        profile = pipeline.start(config)
        
        # 获取深度传感器信息
        depth_sensor = profile.get_device().first_depth_sensor()
        depth_scale = depth_sensor.get_depth_scale()
        print(f"深度比例: {depth_scale}")
        
        # 测试数据获取
        print("测试数据获取...")
        frame_count = 0
        start_time = time.time()
        
        for i in range(30):  # 获取30帧测试
            frames = pipeline.wait_for_frames()
            depth_frame = frames.get_depth_frame()
            color_frame = frames.get_color_frame()
            
            if depth_frame and color_frame:
                frame_count += 1
                
                # 获取图像数据
                depth_image = np.asanyarray(depth_frame.get_data())
                color_image = np.asanyarray(color_frame.get_data())
                
                # 计算一些统计信息
                depth_mean = np.mean(depth_image[depth_image > 0])
                depth_valid_pixels = np.count_nonzero(depth_image)
                
                if i % 10 == 0:  # 每10帧打印一次信息
                    print(f"  帧 {i+1}: 深度={depth_frame.get_width()}x{depth_frame.get_height()}, "
                          f"彩色={color_frame.get_width()}x{color_frame.get_height()}")
                    print(f"    平均深度: {depth_mean:.2f}mm, 有效像素: {depth_valid_pixels}")
        
        end_time = time.time()
        fps = frame_count / (end_time - start_time)
        
        print(f"\n✅ 数据捕获成功!")
        print(f"获取了 {frame_count} 帧数据")
        print(f"平均帧率: {fps:.1f} FPS")
        
        # 停止pipeline
        pipeline.stop()
        return True
        
    except Exception as e:
        print(f"❌ 数据捕获失败: {e}")
        return False

def main():
    """主函数"""
    print("RealSense新编译库测试")
    print("=" * 50)
    
    # 测试设备检测
    detection_success = test_device_detection()
    
    if detection_success:
        # 测试数据捕获
        capture_success = test_data_capture()
        
        if capture_success:
            print("\n" + "=" * 50)
            print("🎉 所有测试通过! L515设备工作正常")
            print("\n现在可以运行图像显示程序!")
        else:
            print("\n⚠️  设备检测成功，但数据捕获失败")
    else:
        print("\n❌ 设备检测失败")
        print("\n可能的原因:")
        print("1. 设备未正确连接")
        print("2. 需要重新插拔设备")
        print("3. 需要重启系统")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RS485设备扫描器
自动检测波特率和设备
"""

import serial
import time
import threading
from datetime import datetime

class RS485Scanner:
    def __init__(self, port='/dev/ttyUSB0'):
        self.port = port
        self.common_baudrates = [9600, 19200, 38400, 57600, 115200, 4800, 2400, 1200]
        self.running = False
        
    def test_baudrate(self, baudrate, duration=3):
        """测试指定波特率"""
        print(f"\n测试波特率: {baudrate}")
        
        try:
            ser = serial.Serial(
                port=self.port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            
            if not ser.is_open:
                print(f"  ✗ 无法打开串口")
                return False, []
                
            ser.flushInput()
            ser.flushOutput()
            
            data_received = []
            start_time = time.time()
            
            print(f"  监听 {duration} 秒...")
            
            while time.time() - start_time < duration:
                if ser.in_waiting > 0:
                    data = ser.read(ser.in_waiting)
                    if data:
                        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                        data_received.append((timestamp, data))
                        print(f"  [{timestamp}] 收到 {len(data)} 字节: {data.hex().upper()}")
                
                time.sleep(0.01)
            
            ser.close()
            
            if data_received:
                print(f"  ✓ 在波特率 {baudrate} 下收到 {len(data_received)} 条数据")
                return True, data_received
            else:
                print(f"  - 在波特率 {baudrate} 下未收到数据")
                return False, []
                
        except Exception as e:
            print(f"  ✗ 错误: {e}")
            return False, []
    
    def scan_all_baudrates(self):
        """扫描所有常用波特率"""
        print(f"=== 扫描RS485设备: {self.port} ===")
        
        results = {}
        
        for baudrate in self.common_baudrates:
            success, data = self.test_baudrate(baudrate, duration=2)
            if success:
                results[baudrate] = data
        
        return results
    
    def send_modbus_queries(self, baudrate):
        """发送Modbus查询测试设备响应"""
        print(f"\n=== Modbus设备扫描 (波特率: {baudrate}) ===")
        
        try:
            ser = serial.Serial(
                port=self.port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.5
            )
            
            # 测试从站ID 1-10
            for slave_id in range(1, 11):
                print(f"测试从站ID: {slave_id}")
                
                # 读取保持寄存器 (功能码03)
                query = bytearray([slave_id, 0x03, 0x00, 0x00, 0x00, 0x01])
                crc = self._calculate_crc16(query)
                query.extend(crc.to_bytes(2, 'little'))
                
                ser.flushInput()
                ser.write(query)
                print(f"  发送: {query.hex().upper()}")
                
                time.sleep(0.1)
                
                if ser.in_waiting > 0:
                    response = ser.read(ser.in_waiting)
                    print(f"  响应: {response.hex().upper()}")
                    
                    if len(response) >= 3:
                        if response[0] == slave_id and response[1] == 0x03:
                            print(f"  ✓ 找到Modbus设备，从站ID: {slave_id}")
                        elif response[1] & 0x80:
                            print(f"  ! Modbus错误响应，错误码: {response[2]}")
                else:
                    print(f"  - 无响应")
                
                time.sleep(0.1)
            
            ser.close()
            
        except Exception as e:
            print(f"Modbus扫描错误: {e}")
    
    def _calculate_crc16(self, data):
        """计算Modbus CRC16"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 1:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        return crc
    
    def continuous_monitor(self, baudrate=9600):
        """持续监控指定波特率"""
        print(f"\n=== 持续监控模式 (波特率: {baudrate}) ===")
        print("按Ctrl+C停止...")
        
        try:
            ser = serial.Serial(
                port=self.port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            
            self.running = True
            
            while self.running:
                if ser.in_waiting > 0:
                    data = ser.read(ser.in_waiting)
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    
                    # HEX显示
                    hex_str = ' '.join([f'{b:02X}' for b in data])
                    
                    # ASCII显示
                    ascii_str = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in data])
                    
                    print(f"[{timestamp}] {len(data):3d}字节 | {hex_str:<30} | {ascii_str}")
                
                time.sleep(0.01)
            
            ser.close()
            
        except KeyboardInterrupt:
            self.running = False
            print("\n监控已停止")
        except Exception as e:
            print(f"监控错误: {e}")
    
    def analyze_data_pattern(self, data_list):
        """分析数据模式"""
        if not data_list:
            return
            
        print(f"\n=== 数据分析 ===")
        print(f"总数据包数: {len(data_list)}")
        
        # 分析数据长度分布
        lengths = [len(data) for _, data in data_list]
        print(f"数据长度范围: {min(lengths)} - {max(lengths)} 字节")
        
        # 分析常见数据模式
        patterns = {}
        for _, data in data_list:
            pattern = data.hex().upper()
            patterns[pattern] = patterns.get(pattern, 0) + 1
        
        print(f"不同数据模式数: {len(patterns)}")
        
        # 显示最常见的模式
        if patterns:
            sorted_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)
            print("最常见的数据模式:")
            for i, (pattern, count) in enumerate(sorted_patterns[:5]):
                print(f"  {i+1}. {pattern} (出现{count}次)")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='RS485设备扫描器')
    parser.add_argument('--port', default='/dev/ttyUSB0', help='串口设备')
    parser.add_argument('--mode', choices=['scan', 'modbus', 'monitor'], 
                       default='scan', help='工作模式')
    parser.add_argument('--baudrate', type=int, default=9600, help='波特率(monitor模式)')
    
    args = parser.parse_args()
    
    scanner = RS485Scanner(args.port)
    
    if args.mode == 'scan':
        results = scanner.scan_all_baudrates()
        
        print(f"\n=== 扫描结果汇总 ===")
        if results:
            for baudrate, data_list in results.items():
                print(f"\n波特率 {baudrate}: 收到 {len(data_list)} 条数据")
                scanner.analyze_data_pattern(data_list)
        else:
            print("未在任何波特率下检测到数据")
            
    elif args.mode == 'modbus':
        # 先扫描找到有数据的波特率
        results = scanner.scan_all_baudrates()
        if results:
            for baudrate in results.keys():
                scanner.send_modbus_queries(baudrate)
        else:
            print("未检测到数据，使用默认波特率9600进行Modbus扫描")
            scanner.send_modbus_queries(9600)
            
    elif args.mode == 'monitor':
        scanner.continuous_monitor(args.baudrate)

if __name__ == "__main__":
    main()

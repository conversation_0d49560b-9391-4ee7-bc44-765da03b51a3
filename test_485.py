#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
485串口通信测试脚本
"""

import serial
import time
import sys

def test_485_communication():
    """测试485通信"""
    try:
        # 配置串口参数
        port = '/dev/ttyUSB0'
        baudrate = 9600  # 常用波特率，可根据设备调整
        
        print(f"正在连接串口: {port}")
        print(f"波特率: {baudrate}")
        
        # 打开串口
        ser = serial.Serial(
            port=port,
            baudrate=baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=1
        )
        
        if ser.is_open:
            print("串口连接成功!")
            print(f"串口信息: {ser}")
            
            # 清空缓冲区
            ser.flushInput()
            ser.flushOutput()
            
            # 测试发送数据
            test_data = b'\x01\x03\x00\x00\x00\x01\x84\x0A'  # 示例Modbus RTU查询命令
            print(f"发送测试数据: {test_data.hex()}")
            
            ser.write(test_data)
            time.sleep(0.1)
            
            # 读取响应
            if ser.in_waiting > 0:
                response = ser.read(ser.in_waiting)
                print(f"接收到响应: {response.hex()}")
                print(f"响应长度: {len(response)} 字节")
            else:
                print("未收到响应数据")
            
            # 持续监听模式
            print("\n进入监听模式 (按Ctrl+C退出)...")
            try:
                while True:
                    if ser.in_waiting > 0:
                        data = ser.read(ser.in_waiting)
                        print(f"[{time.strftime('%H:%M:%S')}] 收到数据: {data.hex()} | ASCII: {data}")
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print("\n退出监听模式")
            
        else:
            print("串口连接失败!")
            
    except serial.SerialException as e:
        print(f"串口错误: {e}")
        print("可能的解决方案:")
        print("1. 检查设备是否正确连接")
        print("2. 确认用户在dialout组中: sudo usermod -a -G dialout $USER")
        print("3. 重新登录或重启系统")
        print("4. 检查串口权限: ls -la /dev/ttyUSB0")
        
    except Exception as e:
        print(f"其他错误: {e}")
        
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()
            print("串口已关闭")

def scan_serial_ports():
    """扫描可用的串口"""
    import glob
    
    print("扫描可用串口...")
    ports = glob.glob('/dev/tty[A-Za-z]*')
    
    available_ports = []
    for port in ports:
        try:
            s = serial.Serial(port)
            s.close()
            available_ports.append(port)
        except (OSError, serial.SerialException):
            pass
    
    if available_ports:
        print("可用串口:")
        for port in available_ports:
            print(f"  - {port}")
    else:
        print("未找到可用串口")
    
    return available_ports

if __name__ == "__main__":
    print("=== 485串口通信测试 ===")
    
    # 扫描串口
    scan_serial_ports()
    print()
    
    # 测试通信
    test_485_communication()

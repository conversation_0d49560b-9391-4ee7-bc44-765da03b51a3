#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RS485串口数据读取器
支持多种波特率和协议
"""

import serial
import time
import sys
import argparse
from datetime import datetime

class RS485Reader:
    def __init__(self, port='/dev/ttyUSB0', baudrate=9600, timeout=1):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = None
        
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=self.timeout,
                xonxoff=False,
                rtscts=False,
                dsrdtr=False
            )
            
            if self.ser.is_open:
                print(f"✓ 串口连接成功: {self.port}")
                print(f"  波特率: {self.baudrate}")
                print(f"  超时: {self.timeout}s")
                self.ser.flushInput()
                self.ser.flushOutput()
                return True
            else:
                print(f"✗ 串口连接失败: {self.port}")
                return False
                
        except Exception as e:
            print(f"✗ 连接错误: {e}")
            return False
    
    def disconnect(self):
        """断开串口"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口已关闭")
    
    def send_data(self, data):
        """发送数据"""
        if not self.ser or not self.ser.is_open:
            print("串口未连接")
            return False
            
        try:
            if isinstance(data, str):
                data = bytes.fromhex(data.replace(' ', ''))
            
            self.ser.write(data)
            print(f"发送: {data.hex().upper()}")
            return True
            
        except Exception as e:
            print(f"发送错误: {e}")
            return False
    
    def read_data(self, max_bytes=1024):
        """读取数据"""
        if not self.ser or not self.ser.is_open:
            return None
            
        try:
            if self.ser.in_waiting > 0:
                data = self.ser.read(min(self.ser.in_waiting, max_bytes))
                return data
            return None
            
        except Exception as e:
            print(f"读取错误: {e}")
            return None
    
    def monitor_continuous(self, show_ascii=True, show_hex=True):
        """持续监听数据"""
        print("\n=== 开始监听数据 (按Ctrl+C停止) ===")
        print("时间戳          | HEX数据                    | ASCII数据")
        print("-" * 70)
        
        try:
            while True:
                data = self.read_data()
                if data:
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    hex_str = data.hex().upper()
                    
                    # 格式化HEX显示
                    hex_formatted = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])
                    
                    # ASCII显示（只显示可打印字符）
                    ascii_str = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in data])
                    
                    print(f"{timestamp} | {hex_formatted:<25} | {ascii_str}")
                
                time.sleep(0.01)  # 10ms间隔
                
        except KeyboardInterrupt:
            print("\n监听已停止")
    
    def test_modbus_rtu(self, slave_id=1, function_code=3, start_addr=0, count=1):
        """测试Modbus RTU通信"""
        print(f"\n=== Modbus RTU测试 ===")
        print(f"从站ID: {slave_id}, 功能码: {function_code}, 起始地址: {start_addr}, 数量: {count}")
        
        # 构造Modbus RTU查询帧
        frame = bytearray([slave_id, function_code])
        frame.extend(start_addr.to_bytes(2, 'big'))
        frame.extend(count.to_bytes(2, 'big'))
        
        # 计算CRC16
        crc = self._calculate_crc16(frame)
        frame.extend(crc.to_bytes(2, 'little'))
        
        # 发送查询
        if self.send_data(frame):
            time.sleep(0.1)
            
            # 读取响应
            response = self.read_data()
            if response:
                print(f"响应: {response.hex().upper()}")
                self._parse_modbus_response(response)
            else:
                print("未收到响应")
    
    def _calculate_crc16(self, data):
        """计算Modbus CRC16"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 1:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        return crc
    
    def _parse_modbus_response(self, data):
        """解析Modbus响应"""
        if len(data) < 3:
            print("响应数据太短")
            return
            
        slave_id = data[0]
        function_code = data[1]
        
        print(f"从站ID: {slave_id}")
        print(f"功能码: {function_code}")
        
        if function_code & 0x80:  # 错误响应
            error_code = data[2]
            print(f"错误码: {error_code}")
        else:
            if function_code == 3:  # 读保持寄存器
                byte_count = data[2]
                print(f"数据字节数: {byte_count}")
                if len(data) >= 3 + byte_count:
                    values = []
                    for i in range(0, byte_count, 2):
                        value = int.from_bytes(data[3+i:3+i+2], 'big')
                        values.append(value)
                    print(f"寄存器值: {values}")

def main():
    parser = argparse.ArgumentParser(description='RS485串口数据读取器')
    parser.add_argument('--port', default='/dev/ttyUSB0', help='串口设备路径')
    parser.add_argument('--baudrate', type=int, default=9600, help='波特率')
    parser.add_argument('--timeout', type=float, default=1.0, help='超时时间(秒)')
    parser.add_argument('--mode', choices=['monitor', 'modbus'], default='monitor', help='工作模式')
    parser.add_argument('--slave-id', type=int, default=1, help='Modbus从站ID')
    
    args = parser.parse_args()
    
    # 创建读取器
    reader = RS485Reader(args.port, args.baudrate, args.timeout)
    
    try:
        if reader.connect():
            if args.mode == 'monitor':
                reader.monitor_continuous()
            elif args.mode == 'modbus':
                reader.test_modbus_rtu(args.slave_id)
                reader.monitor_continuous()
                
    except Exception as e:
        print(f"程序错误: {e}")
    finally:
        reader.disconnect()

if __name__ == "__main__":
    main()
